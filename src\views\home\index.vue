<template>
  <div class="container" :class="elderModeClass">
    <!-- 头部大图 -->
    <div class="banner">
      <img src="@/assets/home/<USER>" alt="防控治安秩序通告" />
    </div>

    <!-- 功能模块区域 -->
    <div class="modules-container">
      <div class="module-grid">
        <div
          class="module-item"
          v-for="(item, index) in moduleList"
          :key="index"
          @click="goToModule(item)"
        >
          <div class="module-icon">
            <img :src="item.icon" alt="" />
          </div>
          <div class="module-name">{{ item.name }}</div>
        </div>
      </div>
    </div>

    <!-- 底部安全区域，避免被tab栏遮挡 -->
    <div class="bottom-safe-area"></div>
  </div>
</template>

<script>
import { getInfo, loginOther, loginProd } from "@/api/login";
import { getToken, setToken } from "@/util/auth";
import elderMode from "@/mixins/elderMode";

export default {
  name: "Home",
  mixins: [elderMode],
  components: {},
  data() {
    return {
      moduleList: [
        {
          name: "犬类管理",
          icon: require("@/assets/home/<USER>"),
          path: process.env.VUE_APP_BASE_ygf_zzd,
        },
        {
          name: "我要爆料",
          icon: require("@/assets/home/<USER>"),
          path: "/BrokeTheNews",
        },
        {
          name: "568志愿服务",
          icon: require("@/assets/home/<USER>"),
          path: process.env.VUE_APP_BASE_568,
        },
      ],
      volUserId: "",
    };
  },
  async created() {
    // 浙里办自动登陆
    if (!this.vuex_ticket) {
      // 如采集用户信息是异步行为需要先执行这个 BLOCK 埋点
      window.aplus_queue.push({
        action: "aplus.setMetaInfo",
        arguments: ["_hold", "BLOCK"],
      });

      const params = window.location.search;
      let userId = params.split("userId=")[1];
      if (userId) {
        this.vuex("vuex_user_vol_id", userId);
      }

      const ticketId = await this.getTicket();
      // const ticketId = "test";
      this.vuex("vuex_ticket", ticketId);

      // await loginProd()

      //浙里办登陆暂时注释
      await loginOther(ticketId)
        .then((response) => {
          console.log(response, "-----------response-----------");
          setToken(response.data.token);
          const user = response.data.sysUser;
          this.volUserId = response.data.volUserId;
          window.aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: ["_user_nick", user.userName],
          });
          // 设置会员 ID
          window.aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: ["_user_id", user.userId],
          });
          // 如采集用户信息是异步行为，需要在设置完用户信息后执行这个 START 埋点 // 此时被 block 住的日志会携带上用户信息逐条发出
          window.aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: ["_hold", "START"],
          });
          // 其余信息埋点
          this.pvAppInfo();
          // 新版埋点-创建埋点实例对象
          this.vuex(
            "vuex_zwlog",
            new window.ZwLog({
              _user_id: user.userid,
              _user_nick: user.realName,
            })
          );
        })
        .catch((err) => {
          console.log(err);
        });

      await getInfo()
        .then((response) => {
          const user = response.user;
          const userObj = {
            ...user,
            deptName: user?.dept?.deptName || "",
            deptId: user?.dept?.deptId || "",
            nickName: user.nickName,
            id: response?.dogUserQualifi?.id || "",
            realName: user.userName || "",
            mobile: user.phonenumber || "",
            userQualifi: response?.dogUserQualifi || undefined,
            qualifi: response?.dogQualifi || undefined,
            roleType: response.roleType,
            roles: response.roles,
            userType: user.userType,
            userId: user.userId,
          };
          this.vuex("user", userObj);

          window.aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: ["_user_nick", user.userName],
          });
          // 设置会员 ID
          window.aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: ["_user_id", user.userId],
          });
          // 如采集用户信息是异步行为，需要在设置完用户信息后执行这个 START 埋点 // 此时被 block 住的日志会携带上用户信息逐条发出
          window.aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: ["_hold", "START"],
          });
          // 其余信息埋点
          this.pvAppInfo();
          // 新版埋点-创建埋点实例对象
          this.vuex(
            "vuex_zwlog",
            new window.ZwLog({
              _user_id: user.userid,
              _user_nick: user.realName,
            })
          );
        })
        .catch((err) => {
          console.log(err);
        });
    }
  },
  mounted() {
    // this.date = dayjs(new Date()).format('hh:mm')
    window.ZWJSBridge.onReady(async () => {
      window.ZWJSBridge.getUiStyle({})
        .then((result) => {
          // if (result.uiStyle == "normal") {
          //   console.log("标准模式");
          // } else if (result.uiStyle == "elder") {
          //   console.log("适老模式");
          //   let zoom = 1.5;
          //   let fs = parseFloat(
          //       document.getElementsByTagName("html")[0].style.fontSize
          //   );
          //   document.getElementsByTagName("html")[0].style.fontSize = `${fs *
          //   zoom}px`;
          //   this.changeZoom(fs, zoom);
          // }
          this.vuex("vuex_uiStyle", result.uiStyle);
        })
        // 浙里办APP 6.11.0 版本以下版本标准模式兼容
        .catch((error) => {
          console.log("catch 标准模式兼容");
          console.log(error);
        });
    });
    // this.vuex('vuex_uiStyle', 'elder')
  },
  methods: {
    getTicket() {
      return new Promise((resolve, reject) => {
        if (window.ZWJSBridge.ssoTicket) {
          // resolve('f1c1cc5afb374109bc20f07528b73e42')
          window.ZWJSBridge.ssoTicket({}).then((ssoFlag) => {
            console.log(ssoFlag, "ssoFlag");
            if (ssoFlag && ssoFlag.result === true) {
              // 使用 IRS"浙里办"单点登录组件
              if (ssoFlag.ticketId) {
                // TODO 应用方服务端单点登录接口
                resolve(ssoFlag.ticketId);
              } else {
                alert("未获取到ticket");
                // 当"浙里办"单点登录失败或登录态失效时调用 ZWJSBridge.openLink 方法重 新获取 ticketId。
                window.ZWJSBridge.openLink({ type: "reload" });
                reject();
              }
            } else {
              // 使用 IRS【个人/法人单点登录】组件
              alert("ssoTicket调用失败");
              reject();
            }
          });
        } else {
          // 使用 IRS【个人/法人单点登录】组件
          alert("无权限ssoTicket");
          reject();
        }
      });
    },
    // PV埋点
    pvAppInfo() {
      // ZWJSBridge初始化
      window.ZWJSBridge.onReady(async () => {
        // 单页应用PV 日志埋点
        window.aplus_queue.push({
          action: "aplus.setMetaInfo",
          arguments: ["aplus-waiting", "MAN"],
        });

        const { userType } = await window.ZWJSBridge.getUserType();
        const { longitude, latitude } = await window.ZWJSBridge.getLocation();
        window.aplus_queue.push({
          action: "aplus.sendPV",
          arguments: [
            { is_auto: false },
            {
              miniAppId: "2001832175",
              miniAppName: "入口-浙里办",
              long: longitude,
              lati: latitude,
              userType: userType,
            },
          ],
        });
      });
    },
    changeZoom(fs, zoom) {
      /* 字体如果太大则不进行放大 */
      const itemW = document.querySelector(".testWid").clientWidth;
      const modelW = document.querySelector(".module").clientWidth;
      if (itemW > modelW) {
        zoom -= 0.1;
        document.getElementsByTagName("html")[0].style.fontSize = `${
          fs * zoom
        }px`;
        this.changeZoom(fs, zoom);
      }
    },
    async goToModule(item) {
      let token = "";
      const tokenResult = await getToken();
      console.log(tokenResult, "-----------token获取结果-----------");

      if (tokenResult && tokenResult["YGF-MOBILE-Token"]) {
        token = tokenResult["YGF-MOBILE-Token"];
      }

      if (item.path && item.name != "我要爆料") {
        if (item.name == "犬类管理") {
          window.location.href = item.path += `/?token=${token}#/`;
        }
        if (item.name == "568志愿服务") {
          window.location.href = item.path += `/?userId=${this.volUserId}#/`;
        }
      } else {
        this.$router.push(item.path);
      }
    },

    // 强制重置测试方法
    forceResetTest() {
      console.log("执行强制重置测试...");

      // 导入强制重置方法
      import("@/utils/elderUtils")
        .then(({ forceResetStyles }) => {
          forceResetStyles();

          // 同时重置vuex状态
          this.vuex("vuex_uiStyle", "normal");

          this.$toast.success("已强制重置所有样式");
        })
        .catch((error) => {
          console.error("重置失败:", error);
          this.$toast.fail("重置失败");
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  background: #f5f5f5;
  // padding-bottom: 80px; // 增加底部padding，为tab栏留出空间
}

.bottom-safe-area {
  height: 20px; // 额外的安全区域
}

.banner {
  width: 100%;
  padding: 0 16px;
  margin-top: 10px;

  img {
    width: 100%;
    border-radius: 8px;
    height: auto;
  }
}

.modules-container {
  background: #ffffff;
  border-radius: 8px;
  margin: 16px;
  padding: 20px 0;

  .module-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 15px 0;

    .module-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .module-icon {
        width: 40px;
        height: 40px;
        margin-bottom: 8px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .module-name {
        width: 58px;
        font-size: 12px;
        color: #333;
        text-align: center;
      }
    }
  }
}

.card-container {
  padding: 0 16px;

  .card-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    .card-item {
      width: 48%;
      background: #ffffff;
      border-radius: 8px;
      position: relative;

      .card-content {
        display: flex;
        padding: 16px;

        .card-icon {
          width: 35px;
          height: 35px;
          margin-right: 12px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .card-info {
          .card-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
          }

          .card-desc {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="layout-container">
    <router-view />

    <!-- 底部Tab栏 -->
    <van-tabbar
      v-model="active"
      active-color="#1989fa"
      inactive-color="#7d7e80"
      border
      fixed
      placeholder
      safe-area-inset-bottom
    >
      <van-tabbar-item to="/" icon="home-o">
        首页
      </van-tabbar-item>
      <van-tabbar-item to="/personCenter" icon="setting-o">
        设置
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>

export default {
  name: 'LayoutIndex',
  components: {},
  props: {},
  data() {
    return {
      active: 0
    }
  },
  computed: {

  },
  watch: {
    '$route'(val) {
      const path = val.path
      // 根据当前路由路径设置active状态
      if (path === '/' && this.active !== 0) {
        this.active = 0
      } else if (path === '/personCenter' && this.active !== 1) {
        this.active = 1
      }
    }
  },
  mounted() {
    // 初始化时设置正确的active状态
    this.setActiveByRoute()
  },
  created() {},
  methods: {
    // 根据当前路由设置active状态
    setActiveByRoute() {
      const path = this.$route.path
      if (path === '/') {
        this.active = 0
      } else if (path === '/personCenter') {
        this.active = 1
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.layout-container {
  min-height: 100vh;
  position: relative;
}

// 确保tab栏样式正确
:deep(.van-tabbar) {
  background: #ffffff;
  border-top: 1px solid #ebedf0;

  .van-tabbar-item {
    font-size: 12px;

    &--active {
      color: #1989fa;
    }
  }
}
</style>
